{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "camera_avfoundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_secure_storage_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_darwin-0.1.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_maps_flutter_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "integration_test", "path": "/home/<USER>/snap/flutter/common/flutter/packages/integration_test/", "native_build": true, "dependencies": [], "dev_dependency": true}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-7.0.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "camera_android_camerax", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/camera_android_camerax-0.6.18/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "dynamic_color", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-10.0.0-beta.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_maps_flutter_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.1/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "image_picker_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "integration_test", "path": "/home/<USER>/snap/flutter/common/flutter/packages/integration_test/", "native_build": true, "dependencies": [], "dev_dependency": true}, {"name": "path_provider_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-7.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "dynamic_color", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_secure_storage_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_darwin-0.1.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_macos"], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-7.0.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "dynamic_color", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-2.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_linux"], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "dynamic_color", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-4.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "camera_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/", "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_core_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.6/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "flutter_secure_storage_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-2.0.0/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.2.2/", "dependencies": [], "dev_dependency": false}, {"name": "google_maps_flutter_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12/", "dependencies": [], "dev_dependency": false}, {"name": "image_picker_for_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-7.0.0/", "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "camera", "dependencies": ["camera_android_camerax", "camera_avfoundation", "camera_web", "flutter_plugin_android_lifecycle"]}, {"name": "camera_android_camerax", "dependencies": []}, {"name": "camera_avfoundation", "dependencies": []}, {"name": "camera_web", "dependencies": []}, {"name": "connectivity_plus", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "dynamic_color", "dependencies": []}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_darwin", "flutter_secure_storage_linux", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_darwin", "dependencies": []}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "flutter_tts", "dependencies": []}, {"name": "google_maps_flutter", "dependencies": ["google_maps_flutter_android", "google_maps_flutter_ios", "google_maps_flutter_web"]}, {"name": "google_maps_flutter_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "google_maps_flutter_ios", "dependencies": []}, {"name": "google_maps_flutter_web", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "integration_test", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "speech_to_text", "dependencies": []}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}], "date_created": "2025-05-26 16:30:03.369490", "version": "3.32.0", "swift_package_manager_enabled": {"ios": false, "macos": false}}