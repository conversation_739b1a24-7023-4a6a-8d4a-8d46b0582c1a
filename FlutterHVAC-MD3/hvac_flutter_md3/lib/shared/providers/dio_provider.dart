import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'logger_provider.dart'; // Import the logger provider

final dioProvider = Provider<Dio>((ref) {
  final logger = ref.watch(loggerProvider); // Get the logger instance

  final dio = Dio(const BaseOptions( // Added const
    connectTimeout: Duration(seconds: 10),
    receiveTimeout: Duration(seconds: 10),
    // You can add other base options here, like headers
  ));

  dio.interceptors.add(InterceptorsWrapper(
    onRequest: (options, handler) {
      logger.d('REQUEST[${options.method}] => PATH: ${options.path} => DATA: ${options.data}');
      return handler.next(options);
    },
    onResponse: (response, handler) {
      logger.d('RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path} => DATA: ${response.data}');
      return handler.next(response);
    },
    onError: (DioException e, handler) {
      logger.e('ERROR[${e.response?.statusCode}] => PATH: ${e.requestOptions.path} => MESSAGE: ${e.message}');
      // You can add custom error handling here, e.g., show a snackbar, refresh token
      if (e.response?.statusCode == 401) {
        // Handle unauthorized error, e.g., redirect to login
        logger.w('Unauthorized access. Redirecting to login...');
      } else if (e.response?.statusCode == 404) {
        logger.w('Resource not found.');
      }
      return handler.next(e);
    },
  ));

  return dio;
});