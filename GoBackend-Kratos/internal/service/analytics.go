package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	
	"gobackend-hvac-kratos/internal/biz"
)

// 📊 Analytics Service - Advanced Dashboard Analytics
// GoBackend-Kratos HVAC CRM System

// AnalyticsService provides advanced analytics and dashboard functionality
type AnalyticsService struct {
	uc  *biz.AnalyticsUsecase
	log *log.Helper
}

// NewAnalyticsService creates a new analytics service instance
func NewAnalyticsService(uc *biz.AnalyticsUsecase, logger log.Logger) *AnalyticsService {
	return &AnalyticsService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

// ============================================================================
// 📊 SERVICE METHODS
// ============================================================================

// GetExecutiveDashboard returns executive dashboard data
func (s *AnalyticsService) GetExecutiveDashboard(ctx context.Context) (*biz.ExecutiveDashboardSummary, error) {
	s.log.WithContext(ctx).Info("📊 Getting executive dashboard")
	return s.uc.GetExecutiveDashboard(ctx)
}

// GetCustomerInsights returns customer insights dashboard data
func (s *AnalyticsService) GetCustomerInsights(ctx context.Context) ([]*biz.CustomerInsight, error) {
	s.log.WithContext(ctx).Info("📊 Getting customer insights")
	return s.uc.GetCustomerInsights(ctx)
}

// GetOperationalDashboard returns operational dashboard data
func (s *AnalyticsService) GetOperationalDashboard(ctx context.Context) (*biz.OperationalAnalytics, error) {
	s.log.WithContext(ctx).Info("📊 Getting operational dashboard")
	return s.uc.GetOperationalDashboard(ctx)
}

// GetPerformanceTrends returns performance trends data
func (s *AnalyticsService) GetPerformanceTrends(ctx context.Context, weeks int) ([]*biz.PerformanceTrend, error) {
	s.log.WithContext(ctx).Infof("📊 Getting performance trends for %d weeks", weeks)
	return s.uc.GetPerformanceTrends(ctx, weeks)
}

// GetKPIs returns KPI data
func (s *AnalyticsService) GetKPIs(ctx context.Context, category string) ([]*biz.KPI, error) {
	s.log.WithContext(ctx).Infof("📊 Getting KPIs for category: %s", category)
	return s.uc.GetKPIs(ctx, category)
}

// UpdateKPI updates or creates a KPI
func (s *AnalyticsService) UpdateKPI(ctx context.Context, kpiName, category string, value float64, target *float64) error {
	s.log.WithContext(ctx).Infof("📊 Updating KPI: %s = %.2f", kpiName, value)
	return s.uc.UpdateKPI(ctx, kpiName, category, value, target)
}

// GetRealTimeMetrics returns real-time system metrics
func (s *AnalyticsService) GetRealTimeMetrics(ctx context.Context) (map[string]interface{}, error) {
	s.log.WithContext(ctx).Info("📊 Getting real-time metrics")
	return s.uc.GetRealTimeMetrics(ctx)
}

// GetDashboardWidgets returns dashboard widgets
func (s *AnalyticsService) GetDashboardWidgets(ctx context.Context, category string) ([]*biz.DashboardWidget, error) {
	s.log.WithContext(ctx).Infof("📊 Getting dashboard widgets for: %s", category)
	return s.uc.GetDashboardWidgets(ctx, category)
}

// CreateDashboardWidget creates a new dashboard widget
func (s *AnalyticsService) CreateDashboardWidget(ctx context.Context, widget *biz.DashboardWidget) error {
	s.log.WithContext(ctx).Infof("📊 Creating dashboard widget: %s", widget.WidgetName)
	return s.uc.CreateDashboardWidget(ctx, widget)
}

// CalculateCustomerAnalytics calculates and updates customer analytics
func (s *AnalyticsService) CalculateCustomerAnalytics(ctx context.Context, customerID uint) error {
	s.log.WithContext(ctx).Infof("📊 Calculating customer analytics for ID: %d", customerID)
	return s.uc.CalculateCustomerAnalytics(ctx, customerID)
}

// UpdateRevenueAnalytics updates revenue analytics
func (s *AnalyticsService) UpdateRevenueAnalytics(ctx context.Context, date time.Time, category string, revenue float64, jobsCount int) error {
	s.log.WithContext(ctx).Infof("📊 Updating revenue analytics: %.2f for %s", revenue, date.Format("2006-01-02"))
	return s.uc.UpdateRevenueAnalytics(ctx, date, category, revenue, jobsCount)
}

// UpdateOperationalAnalytics updates operational analytics
func (s *AnalyticsService) UpdateOperationalAnalytics(ctx context.Context, date time.Time, data *biz.OperationalAnalytics) error {
	s.log.WithContext(ctx).Infof("📊 Updating operational analytics for %s", date.Format("2006-01-02"))
	return s.uc.UpdateOperationalAnalytics(ctx, date, data)
}

// HealthCheck performs a health check on the analytics service
func (s *AnalyticsService) HealthCheck(ctx context.Context) error {
	s.log.WithContext(ctx).Info("📊 Performing analytics service health check")
	
	// Test basic functionality by getting real-time metrics
	_, err := s.uc.GetRealTimeMetrics(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("❌ Analytics service health check failed: %v", err)
		return err
	}
	
	s.log.WithContext(ctx).Info("✅ Analytics service health check passed")
	return nil
}